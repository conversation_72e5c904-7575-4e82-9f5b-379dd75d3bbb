{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 18, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/Projects/browerbase-mvp/frontend/src/components/Header.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/Header.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Header.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA6R,GAC1T,2DACA", "debugId": null}}, {"offset": {"line": 30, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/Projects/browerbase-mvp/frontend/src/components/Header.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/Header.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Header.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAyQ,GACtS,uCACA", "debugId": null}}, {"offset": {"line": 42, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 50, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/Projects/browerbase-mvp/frontend/src/components/Footer.tsx"], "sourcesContent": ["export default function Footer() {\r\n  return (\r\n    <footer className=\"bg-gray-50 border-t\">\r\n      <div className=\"max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8\">\r\n        <div className=\"flex justify-between items-center\">\r\n          <div className=\"text-sm text-gray-500\">\r\n            © 2025 Browserbase + CrewAI POC. Built with Next.js and Tailwind CSS.\r\n          </div>\r\n          <div className=\"flex space-x-6\">\r\n            <a \r\n              href=\"#\" \r\n              className=\"text-gray-400 hover:text-gray-500\"\r\n            >\r\n              Documentation\r\n            </a>\r\n            <a \r\n              href=\"#\" \r\n              className=\"text-gray-400 hover:text-gray-500\"\r\n            >\r\n              Support\r\n            </a>\r\n            <a \r\n              href=\"#\" \r\n              className=\"text-gray-400 hover:text-gray-500\"\r\n            >\r\n              GitHub\r\n            </a>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </footer>\r\n  );\r\n} "], "names": [], "mappings": ";;;;;AAAe,SAAS;IACtB,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCAAwB;;;;;;kCAGvC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,MAAK;gCACL,WAAU;0CACX;;;;;;0CAGD,8OAAC;gCACC,MAAK;gCACL,WAAU;0CACX;;;;;;0CAGD,8OAAC;gCACC,MAAK;gCACL,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb", "debugId": null}}, {"offset": {"line": 128, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/Projects/browerbase-mvp/frontend/src/components/Layout.tsx"], "sourcesContent": ["import Header from './Header';\r\nimport Footer from './Footer';\r\n\r\ninterface LayoutProps {\r\n  children: React.ReactNode;\r\n}\r\n\r\nexport default function Layout({ children }: LayoutProps) {\r\n  return (\r\n    <div className=\"min-h-screen flex flex-col\">\r\n      <Header />\r\n      <main className=\"flex-1 bg-gray-50\">\r\n        {children}\r\n      </main>\r\n      <Footer />\r\n    </div>\r\n  );\r\n} "], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAMe,SAAS,OAAO,EAAE,QAAQ,EAAe;IACtD,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,4HAAA,CAAA,UAAM;;;;;0BACP,8OAAC;gBAAK,WAAU;0BACb;;;;;;0BAEH,8OAAC,4HAAA,CAAA,UAAM;;;;;;;;;;;AAGb", "debugId": null}}, {"offset": {"line": 170, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/Projects/browerbase-mvp/frontend/src/app/page.tsx"], "sourcesContent": ["import Layout from '@/components/Layout';\nimport { DashboardStats } from '@/types';\n\n// Mock data for development\nconst mockStats: DashboardStats = {\n  totalProducts: 0,\n  activeAgents: 0,\n  completedTasks: 0,\n  failedTasks: 0,\n  vendors: [\n    { id: '1', name: 'Te<PERSON>', url: 'https://www.tesco.com/', status: 'active' },\n    { id: '2', name: 'Asda', url: 'https://www.asda.com/', status: 'active' },\n    { id: '3', name: 'Costco', url: 'https://www.costco.com/', status: 'active' },\n  ],\n};\n\nexport default function Home() {\n  return (\n    <Layout>\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        <div className=\"mb-8\">\n          <h1 className=\"text-3xl font-bold text-gray-900\">Dashboard</h1>\n          <p className=\"mt-2 text-gray-600\">\n            Monitor your Browserbase + CrewAI scraping operations\n          </p>\n        </div>\n\n        {/* Stats Cards */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\">\n          <div className=\"bg-white rounded-lg shadow p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <div className=\"w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center\">\n                  <svg className=\"w-5 h-5 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4\" />\n                  </svg>\n                </div>\n              </div>\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-500\">Total Products</p>\n                <p className=\"text-2xl font-semibold text-gray-900\">{mockStats.totalProducts}</p>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white rounded-lg shadow p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <div className=\"w-8 h-8 bg-green-500 rounded-md flex items-center justify-center\">\n                  <svg className=\"w-5 h-5 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                  </svg>\n                </div>\n              </div>\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-500\">Active Agents</p>\n                <p className=\"text-2xl font-semibold text-gray-900\">{mockStats.activeAgents}</p>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white rounded-lg shadow p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <div className=\"w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center\">\n                  <svg className=\"w-5 h-5 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2\" />\n                  </svg>\n                </div>\n              </div>\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-500\">Completed Tasks</p>\n                <p className=\"text-2xl font-semibold text-gray-900\">{mockStats.completedTasks}</p>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white rounded-lg shadow p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <div className=\"w-8 h-8 bg-red-500 rounded-md flex items-center justify-center\">\n                  <svg className=\"w-5 h-5 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                  </svg>\n                </div>\n              </div>\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-500\">Failed Tasks</p>\n                <p className=\"text-2xl font-semibold text-gray-900\">{mockStats.failedTasks}</p>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Vendors Section */}\n        <div className=\"bg-white rounded-lg shadow\">\n          <div className=\"px-6 py-4 border-b border-gray-200\">\n            <h2 className=\"text-lg font-medium text-gray-900\">Connected Vendors</h2>\n          </div>\n          <div className=\"p-6\">\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n              {mockStats.vendors.map((vendor) => (\n                <div key={vendor.id} className=\"border border-gray-200 rounded-lg p-4\">\n                  <div className=\"flex items-center justify-between\">\n                    <div>\n                      <h3 className=\"text-sm font-medium text-gray-900\">{vendor.name}</h3>\n                      <p className=\"text-xs text-gray-500 truncate\">{vendor.url}</p>\n                    </div>\n                    <div className=\"flex items-center\">\n                      <div className={`w-2 h-2 rounded-full ${\n                        vendor.status === 'active' ? 'bg-green-500' : \n                        vendor.status === 'error' ? 'bg-red-500' : 'bg-gray-400'\n                      }`}></div>\n                      <span className=\"ml-2 text-xs text-gray-500 capitalize\">{vendor.status}</span>\n                    </div>\n                  </div>\n                </div>\n              ))}\n            </div>\n          </div>\n        </div>\n\n        {/* Quick Actions */}\n        <div className=\"mt-8 bg-white rounded-lg shadow\">\n          <div className=\"px-6 py-4 border-b border-gray-200\">\n            <h2 className=\"text-lg font-medium text-gray-900\">Quick Actions</h2>\n          </div>\n          <div className=\"p-6\">\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              <button className=\"flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\">\n                Start New Scraping Task\n              </button>\n              <button className=\"flex items-center justify-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\">\n                View Scraped Data\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n    </Layout>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;;;AAGA,4BAA4B;AAC5B,MAAM,YAA4B;IAChC,eAAe;IACf,cAAc;IACd,gBAAgB;IAChB,aAAa;IACb,SAAS;QACP;YAAE,IAAI;YAAK,MAAM;YAAS,KAAK;YAA0B,QAAQ;QAAS;QAC1E;YAAE,IAAI;YAAK,MAAM;YAAQ,KAAK;YAAyB,QAAQ;QAAS;QACxE;YAAE,IAAI;YAAK,MAAM;YAAU,KAAK;YAA2B,QAAQ;QAAS;KAC7E;AACH;AAEe,SAAS;IACtB,qBACE,8OAAC,4HAAA,CAAA,UAAM;kBACL,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAmC;;;;;;sCACjD,8OAAC;4BAAE,WAAU;sCAAqB;;;;;;;;;;;;8BAMpC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;gDAAqB,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DAC5E,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;;;;;;;;;kDAI3E,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DAAoC;;;;;;0DACjD,8OAAC;gDAAE,WAAU;0DAAwC,UAAU,aAAa;;;;;;;;;;;;;;;;;;;;;;;sCAKlF,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;gDAAqB,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DAC5E,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;;;;;;;;;kDAI3E,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DAAoC;;;;;;0DACjD,8OAAC;gDAAE,WAAU;0DAAwC,UAAU,YAAY;;;;;;;;;;;;;;;;;;;;;;;sCAKjF,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;gDAAqB,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DAC5E,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;;;;;;;;;kDAI3E,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DAAoC;;;;;;0DACjD,8OAAC;gDAAE,WAAU;0DAAwC,UAAU,cAAc;;;;;;;;;;;;;;;;;;;;;;;sCAKnF,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;gDAAqB,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DAC5E,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;;;;;;;;;kDAI3E,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DAAoC;;;;;;0DACjD,8OAAC;gDAAE,WAAU;0DAAwC,UAAU,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAOlF,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAG,WAAU;0CAAoC;;;;;;;;;;;sCAEpD,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACZ,UAAU,OAAO,CAAC,GAAG,CAAC,CAAC,uBACtB,8OAAC;wCAAoB,WAAU;kDAC7B,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEAAqC,OAAO,IAAI;;;;;;sEAC9D,8OAAC;4DAAE,WAAU;sEAAkC,OAAO,GAAG;;;;;;;;;;;;8DAE3D,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAW,CAAC,qBAAqB,EACpC,OAAO,MAAM,KAAK,WAAW,iBAC7B,OAAO,MAAM,KAAK,UAAU,eAAe,eAC3C;;;;;;sEACF,8OAAC;4DAAK,WAAU;sEAAyC,OAAO,MAAM;;;;;;;;;;;;;;;;;;uCAXlE,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;8BAqB3B,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAG,WAAU;0CAAoC;;;;;;;;;;;sCAEpD,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAO,WAAU;kDAAuN;;;;;;kDAGzO,8OAAC;wCAAO,WAAU;kDAAmN;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASnP", "debugId": null}}]}