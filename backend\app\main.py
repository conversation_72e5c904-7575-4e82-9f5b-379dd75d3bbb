from fastapi import <PERSON><PERSON><PERSON>
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from contextlib import asynccontextmanager
import uvicorn

try:
    from app.core.config import settings
    from app.api.v1.api import api_router
    from app.websockets.manager import websocket_manager
except ImportError:
    # For direct execution
    from core.config import settings
    from api.v1.api import api_router
    from websockets.manager import websocket_manager


@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup
    print("Starting up Browserbase + CrewAI POC Backend...")
    yield
    # Shutdown
    print("Shutting down Browserbase + CrewAI POC Backend...")


def create_application() -> FastAPI:
    app = FastAPI(
        title="Browserbase + CrewAI POC API",
        description="Backend API for Browserbase + CrewAI web scraping POC",
        version="1.0.0",
        docs_url="/docs",
        redoc_url="/redoc",
        lifespan=lifespan,
    )

    # Configure CORS
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["http://localhost:3000", "http://127.0.0.1:3000"],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

    # Include API router
    app.include_router(api_router, prefix="/api/v1")

    # Health check endpoint
    @app.get("/health")
    async def health_check():
        return JSONResponse(
            content={
                "status": "healthy",
                "service": "Browserbase + CrewAI POC Backend",
                "version": "1.0.0"
            }
        )

    # WebSocket endpoint
    @app.websocket("/ws")
    async def websocket_endpoint(websocket):
        await websocket_manager.connect(websocket)
        try:
            while True:
                data = await websocket.receive_text()
                await websocket_manager.broadcast(f"Message: {data}")
        except Exception as e:
            print(f"WebSocket error: {e}")
        finally:
            await websocket_manager.disconnect(websocket)

    return app


app = create_application()


if __name__ == "__main__":
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    ) 