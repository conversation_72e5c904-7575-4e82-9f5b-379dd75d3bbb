from fastapi import WebSocket
from typing import List
import json
import asyncio
from datetime import datetime


class WebSocketManager:
    def __init__(self):
        self.active_connections: List[WebSocket] = []

    async def connect(self, websocket: WebSocket):
        await websocket.accept()
        self.active_connections.append(websocket)
        await self.broadcast_system_message("New client connected")

    async def disconnect(self, websocket: WebSocket):
        if websocket in self.active_connections:
            self.active_connections.remove(websocket)
            await self.broadcast_system_message("Client disconnected")

    async def broadcast(self, message: str):
        if self.active_connections:
            await asyncio.gather(
                *[connection.send_text(message) for connection in self.active_connections]
            )

    async def broadcast_json(self, data: dict):
        if self.active_connections:
            message = json.dumps(data)
            await asyncio.gather(
                *[connection.send_text(message) for connection in self.active_connections]
            )

    async def broadcast_system_message(self, message: str):
        data = {
            "type": "system",
            "message": message,
            "timestamp": datetime.now().isoformat()
        }
        await self.broadcast_json(data)

    async def broadcast_task_update(self, task_id: str, status: str, progress: float = None):
        data = {
            "type": "task_update",
            "task_id": task_id,
            "status": status,
            "progress": progress,
            "timestamp": datetime.now().isoformat()
        }
        await self.broadcast_json(data)

    async def broadcast_agent_update(self, agent_id: str, status: str, progress: float = None):
        data = {
            "type": "agent_update",
            "agent_id": agent_id,
            "status": status,
            "progress": progress,
            "timestamp": datetime.now().isoformat()
        }
        await self.broadcast_json(data)

    async def broadcast_product_update(self, product_count: int, vendor: str):
        data = {
            "type": "product_update",
            "product_count": product_count,
            "vendor": vendor,
            "timestamp": datetime.now().isoformat()
        }
        await self.broadcast_json(data)


websocket_manager = WebSocketManager() 