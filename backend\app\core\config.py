from pydantic_settings import BaseSettings
from typing import Optional, List
import os


class Settings(BaseSettings):
    # API Settings
    API_V1_STR: str = "/api/v1"
    PROJECT_NAME: str = "Browserbase + CrewAI POC"
    
    # CORS Settings
    BACKEND_CORS_ORIGINS: List[str] = [
        "http://localhost:3000",
        "http://127.0.0.1:3000",
    ]
    
    # Database Settings
    POSTGRES_SERVER: str = "localhost"
    POSTGRES_USER: str = "postgres"
    POSTGRES_PASSWORD: str = "password"
    POSTGRES_DB: str = "browserbase_poc"
    POSTGRES_PORT: str = "5432"
    
    # MongoDB Settings
    MONGODB_URL: str = "mongodb://localhost:27017"
    MONGODB_DB: str = "browserbase_poc"
    
    # Redis Settings
    REDIS_URL: str = "redis://localhost:6379"
    
    # Browserbase Settings
    BROWSERBASE_API_KEY: Optional[str] = None
    BROWSERBASE_BASE_URL: str = "https://api.browserbase.com"
    
    # CrewAI Settings
    CREWAI_MODEL: str = "gpt-4"
    CREWAI_API_KEY: Optional[str] = None
    
    # Security Settings
    SECRET_KEY: str = "your-secret-key-here"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 60 * 24 * 8  # 8 days
    
    # Logging
    LOG_LEVEL: str = "INFO"
    
    class Config:
        env_file = ".env"
        case_sensitive = True


settings = Settings() 