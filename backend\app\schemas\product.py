from pydantic import BaseModel, Field
from typing import Optional
from datetime import datetime
from .base import BaseSchema, AvailabilityEnum


class ProductBase(BaseModel):
    title: str = Field(..., description="Product title")
    price: float = Field(..., description="Product price")
    currency: str = Field(default="GBP", description="Price currency")
    description: Optional[str] = Field(None, description="Product description")
    image_url: Optional[str] = Field(None, description="Product image URL")
    vendor: str = Field(..., description="Vendor name")
    category: str = Field(..., description="Product category")
    availability: AvailabilityEnum = Field(default=AvailabilityEnum.IN_STOCK, description="Product availability")


class ProductCreate(ProductBase):
    pass


class ProductUpdate(BaseModel):
    title: Optional[str] = None
    price: Optional[float] = None
    currency: Optional[str] = None
    description: Optional[str] = None
    image_url: Optional[str] = None
    vendor: Optional[str] = None
    category: Optional[str] = None
    availability: Optional[AvailabilityEnum] = None


class Product(ProductBase, BaseSchema):
    id: str = Field(..., description="Product ID")
    scraped_at: datetime = Field(..., description="Scraping timestamp")


class ProductList(BaseModel):
    products: list[Product] = Field(..., description="List of products")
    total: int = Field(..., description="Total number of products")
    page: int = Field(..., description="Current page number")
    per_page: int = Field(..., description="Products per page") 