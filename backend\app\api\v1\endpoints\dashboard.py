from fastapi import APIRouter
from typing import Dict, Any
from datetime import datetime

router = APIRouter()


@router.get("/stats")
async def get_dashboard_stats():
    """Get dashboard statistics"""
    # Mock data for development
    stats = {
        "total_products": 0,
        "active_agents": 0,
        "completed_tasks": 0,
        "failed_tasks": 0,
        "vendors": [
            {
                "id": "1",
                "name": "Tesco",
                "url": "https://www.tesco.com/",
                "status": "active"
            },
            {
                "id": "2",
                "name": "Asda",
                "url": "https://www.asda.com/",
                "status": "active"
            },
            {
                "id": "3",
                "name": "Costco",
                "url": "https://www.costco.com/",
                "status": "active"
            }
        ],
        "recent_activity": [
            {
                "type": "task_created",
                "message": "New scraping task created for Tesco",
                "timestamp": datetime.now().isoformat()
            },
            {
                "type": "agent_started",
                "message": "Agent 'tesco-scraper-1' started",
                "timestamp": datetime.now().isoformat()
            }
        ]
    }
    return stats


@router.get("/activity")
async def get_recent_activity():
    """Get recent activity feed"""
    # Mock data for development
    activity = [
        {
            "id": "1",
            "type": "task_created",
            "message": "New scraping task created for Tesco",
            "timestamp": datetime.now().isoformat(),
            "details": {
                "task_id": "task-123",
                "vendor": "Tesco",
                "urls_count": 5
            }
        },
        {
            "id": "2",
            "type": "agent_started",
            "message": "Agent 'tesco-scraper-1' started",
            "timestamp": datetime.now().isoformat(),
            "details": {
                "agent_id": "agent-456",
                "vendor": "Tesco",
                "status": "working"
            }
        },
        {
            "id": "3",
            "type": "product_scraped",
            "message": "Scraped 25 products from Tesco",
            "timestamp": datetime.now().isoformat(),
            "details": {
                "vendor": "Tesco",
                "product_count": 25,
                "category": "Groceries"
            }
        }
    ]
    return {"activities": activity}


@router.get("/system-status")
async def get_system_status():
    """Get system status information"""
    # Mock data for development
    status = {
        "overall_status": "healthy",
        "components": {
            "api_server": "healthy",
            "websocket_server": "healthy",
            "crewai_orchestrator": "healthy",
            "browserbase_manager": "healthy",
            "database": "healthy"
        },
        "last_updated": datetime.now().isoformat(),
        "uptime": "2 hours 15 minutes"
    }
    return status 