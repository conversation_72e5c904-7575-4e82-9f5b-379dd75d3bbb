from fastapi import APIRouter
from app.api.v1.endpoints import vendors, products, scraping_tasks, dashboard

api_router = APIRouter()

api_router.include_router(vendors.router, prefix="/vendors", tags=["vendors"])
api_router.include_router(products.router, prefix="/products", tags=["products"])
api_router.include_router(scraping_tasks.router, prefix="/scraping-tasks", tags=["scraping-tasks"])
api_router.include_router(dashboard.router, prefix="/dashboard", tags=["dashboard"]) 