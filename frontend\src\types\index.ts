// Vendor types
export interface Vendor {
  id: string;
  name: string;
  url: string;
  status: 'active' | 'inactive' | 'error';
}

// Product types
export interface Product {
  id: string;
  title: string;
  price: number;
  currency: string;
  description?: string;
  imageUrl?: string;
  vendor: string;
  category: string;
  availability: 'in_stock' | 'out_of_stock' | 'limited';
  scrapedAt: string;
}

// Agent types
export interface Agent {
  id: string;
  name: string;
  status: 'idle' | 'working' | 'error' | 'completed';
  vendor: string;
  category?: string;
  progress: number;
  startTime?: string;
  endTime?: string;
}

// Scraping task types
export interface ScrapingTask {
  id: string;
  urls: string[];
  vendor: string;
  category?: string;
  status: 'pending' | 'in_progress' | 'completed' | 'failed';
  agents: Agent[];
  createdAt: string;
  updatedAt: string;
}

// Dashboard stats
export interface DashboardStats {
  totalProducts: number;
  activeAgents: number;
  completedTasks: number;
  failedTasks: number;
  vendors: Vendor[];
} 