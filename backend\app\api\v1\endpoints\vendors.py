from fastapi import APIRouter, HTTPException, status
from typing import List
from datetime import datetime
import uuid

from app.schemas.vendor import Vendor, VendorCreate, VendorUpdate, VendorList

router = APIRouter()

# Mock data for development
mock_vendors = [
    {
        "id": "1",
        "name": "Tesco",
        "url": "https://www.tesco.com/",
        "status": "active",
        "created_at": datetime.now(),
        "updated_at": datetime.now()
    },
    {
        "id": "2",
        "name": "Asda",
        "url": "https://www.asda.com/",
        "status": "active",
        "created_at": datetime.now(),
        "updated_at": datetime.now()
    },
    {
        "id": "3",
        "name": "Costco",
        "url": "https://www.costco.com/",
        "status": "active",
        "created_at": datetime.now(),
        "updated_at": datetime.now()
    }
]


@router.get("/", response_model=VendorList)
async def get_vendors():
    """Get all vendors"""
    return VendorList(
        vendors=[Vendor(**vendor) for vendor in mock_vendors],
        total=len(mock_vendors)
    )


@router.get("/{vendor_id}", response_model=Vendor)
async def get_vendor(vendor_id: str):
    """Get a specific vendor by ID"""
    for vendor in mock_vendors:
        if vendor["id"] == vendor_id:
            return Vendor(**vendor)
    raise HTTPException(
        status_code=status.HTTP_404_NOT_FOUND,
        detail="Vendor not found"
    )


@router.post("/", response_model=Vendor, status_code=status.HTTP_201_CREATED)
async def create_vendor(vendor: VendorCreate):
    """Create a new vendor"""
    new_vendor = {
        "id": str(uuid.uuid4()),
        "name": vendor.name,
        "url": vendor.url,
        "status": vendor.status.value,
        "created_at": datetime.now(),
        "updated_at": datetime.now()
    }
    mock_vendors.append(new_vendor)
    return Vendor(**new_vendor)


@router.put("/{vendor_id}", response_model=Vendor)
async def update_vendor(vendor_id: str, vendor_update: VendorUpdate):
    """Update a vendor"""
    for vendor in mock_vendors:
        if vendor["id"] == vendor_id:
            update_data = vendor_update.dict(exclude_unset=True)
            vendor.update(update_data)
            vendor["updated_at"] = datetime.now()
            return Vendor(**vendor)
    raise HTTPException(
        status_code=status.HTTP_404_NOT_FOUND,
        detail="Vendor not found"
    )


@router.delete("/{vendor_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_vendor(vendor_id: str):
    """Delete a vendor"""
    for i, vendor in enumerate(mock_vendors):
        if vendor["id"] == vendor_id:
            mock_vendors.pop(i)
            return
    raise HTTPException(
        status_code=status.HTTP_404_NOT_FOUND,
        detail="Vendor not found"
    ) 