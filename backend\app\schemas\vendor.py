from pydantic import BaseModel, Field
from typing import Optional
from datetime import datetime
from .base import BaseSchema, VendorStatusEnum


class VendorBase(BaseModel):
    name: str = Field(..., description="Vendor name")
    url: str = Field(..., description="Vendor website URL")
    status: VendorStatusEnum = Field(default=VendorStatusEnum.ACTIVE, description="Vendor status")


class VendorCreate(VendorBase):
    pass


class VendorUpdate(BaseModel):
    name: Optional[str] = None
    url: Optional[str] = None
    status: Optional[VendorStatusEnum] = None


class Vendor(VendorBase, BaseSchema):
    id: str = Field(..., description="Vendor ID")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: datetime = Field(..., description="Last update timestamp")


class VendorList(BaseModel):
    vendors: list[Vendor] = Field(..., description="List of vendors")
    total: int = Field(..., description="Total number of vendors") 