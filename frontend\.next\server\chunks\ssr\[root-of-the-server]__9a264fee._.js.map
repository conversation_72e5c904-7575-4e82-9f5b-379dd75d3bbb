{"version": 3, "sources": [], "sections": [{"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/Projects/browerbase-mvp/frontend/src/components/Header.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport Link from 'next/link';\r\n\r\nexport default function Header() {\r\n  return (\r\n    <header className=\"bg-white shadow-sm border-b\">\r\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\r\n        <div className=\"flex justify-between items-center h-16\">\r\n          <div className=\"flex items-center\">\r\n            <Link href=\"/\" className=\"flex items-center\">\r\n              <div className=\"flex-shrink-0\">\r\n                <h1 className=\"text-xl font-bold text-gray-900\">\r\n                  Browserbase + CrewAI POC\r\n                </h1>\r\n              </div>\r\n            </Link>\r\n          </div>\r\n          \r\n          <nav className=\"hidden md:flex space-x-8\">\r\n            <Link \r\n              href=\"/\" \r\n              className=\"text-gray-500 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium\"\r\n            >\r\n              Dashboard\r\n            </Link>\r\n            <Link \r\n              href=\"/scraping\" \r\n              className=\"text-gray-500 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium\"\r\n            >\r\n              Scraping\r\n            </Link>\r\n            <Link \r\n              href=\"/data\" \r\n              className=\"text-gray-500 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium\"\r\n            >\r\n              Data\r\n            </Link>\r\n          </nav>\r\n          \r\n          <div className=\"flex items-center space-x-4\">\r\n            <div className=\"flex items-center space-x-2\">\r\n              <div className=\"w-2 h-2 bg-green-500 rounded-full\"></div>\r\n              <span className=\"text-sm text-gray-500\">System Online</span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </header>\r\n  );\r\n} "], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIe,SAAS;IACtB,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;sCACvB,cAAA,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAG,WAAU;8CAAkC;;;;;;;;;;;;;;;;;;;;;kCAOtD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;0CAGD,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;0CAGD,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;;;;;;;kCAKH,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAK,WAAU;8CAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOtD", "debugId": null}}]}