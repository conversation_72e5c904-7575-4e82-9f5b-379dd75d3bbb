from pydantic import BaseModel, Field
from typing import Optional, List
from datetime import datetime
from .base import BaseSchema, StatusEnum, AgentStatusEnum


class AgentBase(BaseModel):
    name: str = Field(..., description="Agent name")
    status: AgentStatusEnum = Field(default=AgentStatusEnum.IDLE, description="Agent status")
    vendor: str = Field(..., description="Target vendor")
    category: Optional[str] = Field(None, description="Product category")
    progress: float = Field(default=0.0, description="Progress percentage (0-100)")


class Agent(AgentBase, BaseSchema):
    id: str = Field(..., description="Agent ID")
    start_time: Optional[datetime] = Field(None, description="Start time")
    end_time: Optional[datetime] = Field(None, description="End time")


class ScrapingTaskBase(BaseModel):
    urls: List[str] = Field(..., description="URLs to scrape")
    vendor: str = Field(..., description="Target vendor")
    category: Optional[str] = Field(None, description="Product category")
    status: StatusEnum = Field(default=StatusEnum.PENDING, description="Task status")


class ScrapingTaskCreate(ScrapingTaskBase):
    pass


class ScrapingTaskUpdate(BaseModel):
    urls: Optional[List[str]] = None
    vendor: Optional[str] = None
    category: Optional[str] = None
    status: Optional[StatusEnum] = None


class ScrapingTask(ScrapingTaskBase, BaseSchema):
    id: str = Field(..., description="Task ID")
    agents: List[Agent] = Field(default=[], description="Assigned agents")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: datetime = Field(..., description="Last update timestamp")


class ScrapingTaskList(BaseModel):
    tasks: List[ScrapingTask] = Field(..., description="List of scraping tasks")
    total: int = Field(..., description="Total number of tasks") 