from fastapi import APIRouter, HTTPException, status, Query
from typing import List, Optional
from datetime import datetime
import uuid

from app.schemas.product import Product, ProductCreate, ProductUpdate, ProductList

router = APIRouter()

# Mock data for development
mock_products = []


@router.get("/", response_model=ProductList)
async def get_products(
    page: int = Query(1, ge=1, description="Page number"),
    per_page: int = Query(20, ge=1, le=100, description="Products per page"),
    vendor: Optional[str] = Query(None, description="Filter by vendor"),
    category: Optional[str] = Query(None, description="Filter by category")
):
    """Get all products with pagination and filtering"""
    filtered_products = mock_products
    
    if vendor:
        filtered_products = [p for p in filtered_products if p["vendor"] == vendor]
    
    if category:
        filtered_products = [p for p in filtered_products if p["category"] == category]
    
    start_idx = (page - 1) * per_page
    end_idx = start_idx + per_page
    paginated_products = filtered_products[start_idx:end_idx]
    
    return ProductList(
        products=[Product(**product) for product in paginated_products],
        total=len(filtered_products),
        page=page,
        per_page=per_page
    )


@router.get("/{product_id}", response_model=Product)
async def get_product(product_id: str):
    """Get a specific product by ID"""
    for product in mock_products:
        if product["id"] == product_id:
            return Product(**product)
    raise HTTPException(
        status_code=status.HTTP_404_NOT_FOUND,
        detail="Product not found"
    )


@router.post("/", response_model=Product, status_code=status.HTTP_201_CREATED)
async def create_product(product: ProductCreate):
    """Create a new product"""
    new_product = {
        "id": str(uuid.uuid4()),
        "title": product.title,
        "price": product.price,
        "currency": product.currency,
        "description": product.description,
        "image_url": product.image_url,
        "vendor": product.vendor,
        "category": product.category,
        "availability": product.availability.value,
        "scraped_at": datetime.now()
    }
    mock_products.append(new_product)
    return Product(**new_product)


@router.put("/{product_id}", response_model=Product)
async def update_product(product_id: str, product_update: ProductUpdate):
    """Update a product"""
    for product in mock_products:
        if product["id"] == product_id:
            update_data = product_update.dict(exclude_unset=True)
            product.update(update_data)
            return Product(**product)
    raise HTTPException(
        status_code=status.HTTP_404_NOT_FOUND,
        detail="Product not found"
    )


@router.delete("/{product_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_product(product_id: str):
    """Delete a product"""
    for i, product in enumerate(mock_products):
        if product["id"] == product_id:
            mock_products.pop(i)
            return
    raise HTTPException(
        status_code=status.HTTP_404_NOT_FOUND,
        detail="Product not found"
    ) 