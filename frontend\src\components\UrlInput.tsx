'use client';

import React, { useState, useCallback } from 'react';

interface UrlInputProps {
  onUrlsChange: (urls: string[]) => void;
  placeholder?: string;
  className?: string;
}

interface ParsedUrl {
  url: string;
  isValid: boolean;
  error?: string;
}

export default function UrlInput({ onUrlsChange, placeholder = "Paste URLs here (one per line)...", className = "" }: UrlInputProps) {
  const [inputValue, setInputValue] = useState('');
  const [parsedUrls, setParsedUrls] = useState<ParsedUrl[]>([]);
  const [hasError, setHasError] = useState(false);

  // URL validation regex
  const urlRegex = /^https?:\/\/[^\s/$.?#].[^\s]*$/i;

  // Parse and validate URLs
  const parseUrls = useCallback((text: string): ParsedUrl[] => {
    const lines = text.split('\n').map(line => line.trim()).filter(line => line.length > 0);
    
    return lines.map((line, index) => {
      const isValid = urlRegex.test(line);
      return {
        url: line,
        isValid,
        error: isValid ? undefined : 'Invalid URL format'
      };
    });
  }, []);

  // Handle input change
  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const value = e.target.value;
    setInputValue(value);
    
    const parsed = parseUrls(value);
    setParsedUrls(parsed);
    
    // Extract valid URLs
    const validUrls = parsed.filter(p => p.isValid).map(p => p.url);
    setHasError(parsed.some(p => !p.isValid));
    
    // Notify parent component
    onUrlsChange(validUrls);
  };

  // Remove duplicates
  const removeDuplicates = () => {
    const uniqueUrls = Array.from(new Set(parsedUrls.map(p => p.url)));
    const uniqueParsed = uniqueUrls.map(url => {
      const existing = parsedUrls.find(p => p.url === url);
      return existing || { url, isValid: urlRegex.test(url) };
    });
    
    const newInputValue = uniqueParsed.map(p => p.url).join('\n');
    setInputValue(newInputValue);
    setParsedUrls(uniqueParsed);
    
    const validUrls = uniqueParsed.filter(p => p.isValid).map(p => p.url);
    setHasError(uniqueParsed.some(p => !p.isValid));
    onUrlsChange(validUrls);
  };

  // Clear all URLs
  const clearUrls = () => {
    setInputValue('');
    setParsedUrls([]);
    setHasError(false);
    onUrlsChange([]);
  };

  const validCount = parsedUrls.filter(p => p.isValid).length;
  const invalidCount = parsedUrls.filter(p => !p.isValid).length;
  const totalCount = parsedUrls.length;

  return (
    <div className={`space-y-4 ${className}`}>
      {/* URL Input Area */}
      <div className="space-y-2">
        <label htmlFor="url-input" className="block text-sm font-medium text-gray-700">
          URLs to Scrape
        </label>
        <textarea
          id="url-input"
          value={inputValue}
          onChange={handleInputChange}
          placeholder={placeholder}
          className={`w-full h-32 px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none ${
            hasError ? 'border-red-300' : 'border-gray-300'
          }`}
        />
      </div>

      {/* URL Statistics */}
      {totalCount > 0 && (
        <div className="flex items-center justify-between text-sm">
          <div className="flex space-x-4">
            <span className="text-green-600">
              ✓ {validCount} valid URL{validCount !== 1 ? 's' : ''}
            </span>
            {invalidCount > 0 && (
              <span className="text-red-600">
                ✗ {invalidCount} invalid URL{invalidCount !== 1 ? 's' : ''}
              </span>
            )}
          </div>
          <div className="flex space-x-2">
            {totalCount > 1 && (
              <button
                onClick={removeDuplicates}
                className="px-3 py-1 text-xs bg-gray-100 text-gray-700 rounded hover:bg-gray-200 transition-colors"
              >
                Remove Duplicates
              </button>
            )}
            <button
              onClick={clearUrls}
              className="px-3 py-1 text-xs bg-red-100 text-red-700 rounded hover:bg-red-200 transition-colors"
            >
              Clear All
            </button>
          </div>
        </div>
      )}

      {/* URL Preview */}
      {parsedUrls.length > 0 && (
        <div className="space-y-2">
          <h4 className="text-sm font-medium text-gray-700">URL Preview</h4>
          <div className="max-h-32 overflow-y-auto space-y-1">
            {parsedUrls.map((parsed, index) => (
              <div
                key={index}
                className={`flex items-center space-x-2 p-2 rounded text-sm ${
                  parsed.isValid ? 'bg-green-50 text-green-800' : 'bg-red-50 text-red-800'
                }`}
              >
                <span className="flex-shrink-0">
                  {parsed.isValid ? '✓' : '✗'}
                </span>
                <span className="flex-1 truncate">{parsed.url}</span>
                {!parsed.isValid && (
                  <span className="text-xs opacity-75">{parsed.error}</span>
                )}
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Error Messages */}
      {hasError && (
        <div className="p-3 bg-red-50 border border-red-200 rounded-md">
          <p className="text-sm text-red-800">
            Please fix the invalid URLs above before proceeding.
          </p>
        </div>
      )}
    </div>
  );
} 