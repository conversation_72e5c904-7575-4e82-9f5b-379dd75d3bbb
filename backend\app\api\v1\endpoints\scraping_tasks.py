from fastapi import APIRouter, HTTPException, status
from typing import List
from datetime import datetime
import uuid

from app.schemas.scraping_task import ScrapingTask, ScrapingTaskCreate, ScrapingTaskUpdate, ScrapingTaskList

router = APIRouter()

# Mock data for development
mock_tasks = []


@router.get("/", response_model=ScrapingTaskList)
async def get_scraping_tasks():
    """Get all scraping tasks"""
    return ScrapingTaskList(
        tasks=[ScrapingTask(**task) for task in mock_tasks],
        total=len(mock_tasks)
    )


@router.get("/{task_id}", response_model=ScrapingTask)
async def get_scraping_task(task_id: str):
    """Get a specific scraping task by ID"""
    for task in mock_tasks:
        if task["id"] == task_id:
            return ScrapingTask(**task)
    raise HTTPException(
        status_code=status.HTTP_404_NOT_FOUND,
        detail="Scraping task not found"
    )


@router.post("/", response_model=ScrapingTask, status_code=status.HTTP_201_CREATED)
async def create_scraping_task(task: ScrapingTaskCreate):
    """Create a new scraping task"""
    new_task = {
        "id": str(uuid.uuid4()),
        "urls": task.urls,
        "vendor": task.vendor,
        "category": task.category,
        "status": task.status.value,
        "agents": [],
        "created_at": datetime.now(),
        "updated_at": datetime.now()
    }
    mock_tasks.append(new_task)
    return ScrapingTask(**new_task)


@router.put("/{task_id}", response_model=ScrapingTask)
async def update_scraping_task(task_id: str, task_update: ScrapingTaskUpdate):
    """Update a scraping task"""
    for task in mock_tasks:
        if task["id"] == task_id:
            update_data = task_update.dict(exclude_unset=True)
            task.update(update_data)
            task["updated_at"] = datetime.now()
            return ScrapingTask(**task)
    raise HTTPException(
        status_code=status.HTTP_404_NOT_FOUND,
        detail="Scraping task not found"
    )


@router.delete("/{task_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_scraping_task(task_id: str):
    """Delete a scraping task"""
    for i, task in enumerate(mock_tasks):
        if task["id"] == task_id:
            mock_tasks.pop(i)
            return
    raise HTTPException(
        status_code=status.HTTP_404_NOT_FOUND,
        detail="Scraping task not found"
    )


@router.post("/{task_id}/start", response_model=ScrapingTask)
async def start_scraping_task(task_id: str):
    """Start a scraping task"""
    for task in mock_tasks:
        if task["id"] == task_id:
            task["status"] = "in_progress"
            task["updated_at"] = datetime.now()
            return ScrapingTask(**task)
    raise HTTPException(
        status_code=status.HTTP_404_NOT_FOUND,
        detail="Scraping task not found"
    )


@router.post("/{task_id}/stop", response_model=ScrapingTask)
async def stop_scraping_task(task_id: str):
    """Stop a scraping task"""
    for task in mock_tasks:
        if task["id"] == task_id:
            task["status"] = "cancelled"
            task["updated_at"] = datetime.now()
            return ScrapingTask(**task)
    raise HTTPException(
        status_code=status.HTTP_404_NOT_FOUND,
        detail="Scraping task not found"
    ) 