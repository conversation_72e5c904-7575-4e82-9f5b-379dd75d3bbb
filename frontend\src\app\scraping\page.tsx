'use client';

import React, { useState } from 'react';
import Layout from '@/components/Layout';
import UrlInput from '@/components/UrlInput';

interface ScrapingConfig {
  vendor: string;
  category?: string;
  maxProducts?: number;
  delay?: number;
}

export default function ScrapingPage() {
  const [urls, setUrls] = useState<string[]>([]);
  const [config, setConfig] = useState<ScrapingConfig>({
    vendor: 'tesco',
    category: '',
    maxProducts: 100,
    delay: 1000
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  const vendors = [
    { id: 'tesco', name: 'Tesco', url: 'https://www.tesco.com/' },
    { id: 'asda', name: 'Asda', url: 'https://www.asda.com/' },
    { id: 'costco', name: 'Costco', url: 'https://www.costco.com/' }
  ];

  const categories = [
    'groceries',
    'electronics',
    'clothing',
    'home',
    'beauty',
    'sports',
    'books',
    'toys'
  ];

  const handleUrlsChange = (newUrls: string[]) => {
    setUrls(newUrls);
  };

  const handleConfigChange = (field: keyof ScrapingConfig, value: string | number) => {
    setConfig(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (urls.length === 0) {
      alert('Please enter at least one valid URL');
      return;
    }

    setIsSubmitting(true);
    
    try {
      // TODO: Implement API call to backend
      console.log('Submitting scraping task:', {
        urls,
        config
      });
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      alert('Scraping task created successfully!');
      
      // Reset form
      setUrls([]);
      setConfig({
        vendor: 'tesco',
        category: '',
        maxProducts: 100,
        delay: 1000
      });
    } catch (error) {
      console.error('Error creating scraping task:', error);
      alert('Error creating scraping task. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Layout>
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Create Scraping Task
          </h1>
          <p className="text-gray-600">
            Enter URLs to scrape and configure your scraping parameters.
          </p>
        </div>

        <form onSubmit={handleSubmit} className="space-y-8">
          {/* URL Input Section */}
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">
              URLs to Scrape
            </h2>
            <UrlInput
              onUrlsChange={handleUrlsChange}
              placeholder="https://www.tesco.com/groceries/en-GB/products/123456
https://www.asda.com/product/789012
https://www.costco.com/electronics/..."
            />
          </div>

          {/* Configuration Section */}
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">
              Scraping Configuration
            </h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Vendor Selection */}
              <div>
                <label htmlFor="vendor" className="block text-sm font-medium text-gray-700 mb-2">
                  Target Vendor
                </label>
                <select
                  id="vendor"
                  value={config.vendor}
                  onChange={(e) => handleConfigChange('vendor', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  {vendors.map(vendor => (
                    <option key={vendor.id} value={vendor.id}>
                      {vendor.name}
                    </option>
                  ))}
                </select>
              </div>

              {/* Category Selection */}
              <div>
                <label htmlFor="category" className="block text-sm font-medium text-gray-700 mb-2">
                  Product Category (Optional)
                </label>
                <select
                  id="category"
                  value={config.category}
                  onChange={(e) => handleConfigChange('category', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="">All Categories</option>
                  {categories.map(category => (
                    <option key={category} value={category}>
                      {category.charAt(0).toUpperCase() + category.slice(1)}
                    </option>
                  ))}
                </select>
              </div>

              {/* Max Products */}
              <div>
                <label htmlFor="maxProducts" className="block text-sm font-medium text-gray-700 mb-2">
                  Max Products per URL
                </label>
                <input
                  type="number"
                  id="maxProducts"
                  value={config.maxProducts}
                  onChange={(e) => handleConfigChange('maxProducts', parseInt(e.target.value))}
                  min="1"
                  max="1000"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>

              {/* Delay */}
              <div>
                <label htmlFor="delay" className="block text-sm font-medium text-gray-700 mb-2">
                  Delay Between Requests (ms)
                </label>
                <input
                  type="number"
                  id="delay"
                  value={config.delay}
                  onChange={(e) => handleConfigChange('delay', parseInt(e.target.value))}
                  min="100"
                  max="10000"
                  step="100"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
            </div>
          </div>

          {/* Submit Button */}
          <div className="flex justify-end">
            <button
              type="submit"
              disabled={isSubmitting || urls.length === 0}
              className={`px-6 py-3 rounded-md text-white font-medium transition-colors ${
                isSubmitting || urls.length === 0
                  ? 'bg-gray-400 cursor-not-allowed'
                  : 'bg-blue-600 hover:bg-blue-700'
              }`}
            >
              {isSubmitting ? (
                <div className="flex items-center space-x-2">
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                  <span>Creating Task...</span>
                </div>
              ) : (
                `Start Scraping (${urls.length} URL${urls.length !== 1 ? 's' : ''})`
              )}
            </button>
          </div>
        </form>

        {/* Quick Start Examples */}
        <div className="mt-8 bg-gray-50 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            Quick Start Examples
          </h3>
          <div className="space-y-3">
            <div>
              <h4 className="font-medium text-gray-800">Tesco Groceries</h4>
              <p className="text-sm text-gray-600">
                https://www.tesco.com/groceries/en-GB/products/123456
              </p>
            </div>
            <div>
              <h4 className="font-medium text-gray-800">Asda Electronics</h4>
              <p className="text-sm text-gray-600">
                https://www.asda.com/electronics/789012
              </p>
            </div>
            <div>
              <h4 className="font-medium text-gray-800">Costco Home & Garden</h4>
              <p className="text-sm text-gray-600">
                https://www.costco.com/home-garden/345678
              </p>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
} 